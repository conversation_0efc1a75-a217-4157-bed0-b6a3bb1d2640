<template>
  <div>
    <h1>团队管理</h1>
    <ul v-for="member in team.members" :key="member.id">
      <li>
        {{ member.name }} ({{ member.role }}) - {{ member.status }}
      </li>
    </ul>
    <button @click="updateLeaderStatus">更新领导状态</button>
    <button @click="updateMemberStatus">更新成员状态</button>
  </div>
</template>

<script setup>
import { reactive, watchEffect } from 'vue'
const team = reactive({
  members: [
    { id: 1, name: '<PERSON>', role: 'Leader', status: 'Active' },
    { id: 2, name: '<PERSON>', role: 'Member', status: 'Active' },
    { id: 3, name: '<PERSON>', role: 'Member', status: 'Inactive' },
  ],
})
const updateLeaderStatus = () => {
  const leader = team.members.find((member) => member.role === 'Leader')
  leader.status = leader.status === 'Active' ? 'Inactive' : 'Active'
}
const updateMemberStatus = () => {
  const member = team.members.find((member) => member.role === 'Member')
  member.status = member.status === 'Active' ? 'Inactive' : 'Active'
}
</script>

<style lang="scss" scoped></style>
