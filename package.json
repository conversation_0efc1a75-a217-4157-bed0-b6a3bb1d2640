{"name": "vue-practice", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "format": "prettier --write src/"}, "dependencies": {"vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "prettier": "3.5.3", "sass-embedded": "^1.89.2", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2"}}